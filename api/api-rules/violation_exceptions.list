API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,MutatingWebhook,AdmissionReviewVersions
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,MutatingWebhook,Rules
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,MutatingWebhookConfiguration,Webhooks
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,Rule,APIGroups
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,Rule,APIVersions
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,Rule,Resources
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,RuleWithOperations,Operations
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,ValidatingWebhook,AdmissionReviewVersions
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,ValidatingWebhook,Rules
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,ValidatingWebhookConfiguration,Webhooks
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1,WebhookClientConfig,CABundle
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,MutatingWebhook,AdmissionReviewVersions
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,MutatingWebhook,Rules
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,MutatingWebhookConfiguration,Webhooks
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,Rule,APIGroups
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,Rule,APIVersions
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,Rule,Resources
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,RuleWithOperations,Operations
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,ValidatingWebhook,AdmissionReviewVersions
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,ValidatingWebhook,Rules
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,ValidatingWebhookConfiguration,Webhooks
API rule violation: list_type_missing,k8s.io/api/admissionregistration/v1beta1,WebhookClientConfig,CABundle
API rule violation: list_type_missing,k8s.io/api/apps/v1,DaemonSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1,DeploymentStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1,ReplicaSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1,StatefulSetSpec,VolumeClaimTemplates
API rule violation: list_type_missing,k8s.io/api/apps/v1,StatefulSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1beta1,DeploymentStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1beta1,StatefulSetSpec,VolumeClaimTemplates
API rule violation: list_type_missing,k8s.io/api/apps/v1beta1,StatefulSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1beta2,DaemonSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1beta2,DeploymentStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1beta2,ReplicaSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/apps/v1beta2,StatefulSetSpec,VolumeClaimTemplates
API rule violation: list_type_missing,k8s.io/api/apps/v1beta2,StatefulSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/authentication/v1,TokenRequestSpec,Audiences
API rule violation: list_type_missing,k8s.io/api/authentication/v1,TokenReviewSpec,Audiences
API rule violation: list_type_missing,k8s.io/api/authentication/v1,TokenReviewStatus,Audiences
API rule violation: list_type_missing,k8s.io/api/authentication/v1,UserInfo,Groups
API rule violation: list_type_missing,k8s.io/api/authentication/v1beta1,TokenReviewSpec,Audiences
API rule violation: list_type_missing,k8s.io/api/authentication/v1beta1,TokenReviewStatus,Audiences
API rule violation: list_type_missing,k8s.io/api/authentication/v1beta1,UserInfo,Groups
API rule violation: list_type_missing,k8s.io/api/authorization/v1,NonResourceRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/api/authorization/v1,NonResourceRule,Verbs
API rule violation: list_type_missing,k8s.io/api/authorization/v1,ResourceRule,APIGroups
API rule violation: list_type_missing,k8s.io/api/authorization/v1,ResourceRule,ResourceNames
API rule violation: list_type_missing,k8s.io/api/authorization/v1,ResourceRule,Resources
API rule violation: list_type_missing,k8s.io/api/authorization/v1,ResourceRule,Verbs
API rule violation: list_type_missing,k8s.io/api/authorization/v1,SubjectAccessReviewSpec,Groups
API rule violation: list_type_missing,k8s.io/api/authorization/v1,SubjectRulesReviewStatus,NonResourceRules
API rule violation: list_type_missing,k8s.io/api/authorization/v1,SubjectRulesReviewStatus,ResourceRules
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,NonResourceRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,NonResourceRule,Verbs
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,ResourceRule,APIGroups
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,ResourceRule,ResourceNames
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,ResourceRule,Resources
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,ResourceRule,Verbs
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,SubjectAccessReviewSpec,Groups
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,SubjectRulesReviewStatus,NonResourceRules
API rule violation: list_type_missing,k8s.io/api/authorization/v1beta1,SubjectRulesReviewStatus,ResourceRules
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta1,HorizontalPodAutoscalerSpec,Metrics
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta1,HorizontalPodAutoscalerStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta1,HorizontalPodAutoscalerStatus,CurrentMetrics
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta2,HPAScalingRules,Policies
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta2,HorizontalPodAutoscalerSpec,Metrics
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta2,HorizontalPodAutoscalerStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/autoscaling/v2beta2,HorizontalPodAutoscalerStatus,CurrentMetrics
API rule violation: list_type_missing,k8s.io/api/batch/v1,JobStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/batch/v1beta1,CronJobStatus,Active
API rule violation: list_type_missing,k8s.io/api/batch/v2alpha1,CronJobStatus,Active
API rule violation: list_type_missing,k8s.io/api/core/v1,AvoidPods,PreferAvoidPods
API rule violation: list_type_missing,k8s.io/api/core/v1,Capabilities,Add
API rule violation: list_type_missing,k8s.io/api/core/v1,Capabilities,Drop
API rule violation: list_type_missing,k8s.io/api/core/v1,CephFSPersistentVolumeSource,Monitors
API rule violation: list_type_missing,k8s.io/api/core/v1,CephFSVolumeSource,Monitors
API rule violation: list_type_missing,k8s.io/api/core/v1,ComponentStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/core/v1,ConfigMapProjection,Items
API rule violation: list_type_missing,k8s.io/api/core/v1,ConfigMapVolumeSource,Items
API rule violation: list_type_missing,k8s.io/api/core/v1,Container,Args
API rule violation: list_type_missing,k8s.io/api/core/v1,Container,Command
API rule violation: list_type_missing,k8s.io/api/core/v1,Container,Env
API rule violation: list_type_missing,k8s.io/api/core/v1,Container,EnvFrom
API rule violation: list_type_missing,k8s.io/api/core/v1,Container,VolumeDevices
API rule violation: list_type_missing,k8s.io/api/core/v1,Container,VolumeMounts
API rule violation: list_type_missing,k8s.io/api/core/v1,ContainerImage,Names
API rule violation: list_type_missing,k8s.io/api/core/v1,DownwardAPIProjection,Items
API rule violation: list_type_missing,k8s.io/api/core/v1,DownwardAPIVolumeSource,Items
API rule violation: list_type_missing,k8s.io/api/core/v1,EndpointSubset,Addresses
API rule violation: list_type_missing,k8s.io/api/core/v1,EndpointSubset,NotReadyAddresses
API rule violation: list_type_missing,k8s.io/api/core/v1,EndpointSubset,Ports
API rule violation: list_type_missing,k8s.io/api/core/v1,Endpoints,Subsets
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,Args
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,Command
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,Env
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,EnvFrom
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,Ports
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,VolumeDevices
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainerCommon,VolumeMounts
API rule violation: list_type_missing,k8s.io/api/core/v1,EphemeralContainers,EphemeralContainers
API rule violation: list_type_missing,k8s.io/api/core/v1,ExecAction,Command
API rule violation: list_type_missing,k8s.io/api/core/v1,FCVolumeSource,TargetWWNs
API rule violation: list_type_missing,k8s.io/api/core/v1,FCVolumeSource,WWIDs
API rule violation: list_type_missing,k8s.io/api/core/v1,HTTPGetAction,HTTPHeaders
API rule violation: list_type_missing,k8s.io/api/core/v1,HostAlias,Hostnames
API rule violation: list_type_missing,k8s.io/api/core/v1,ISCSIPersistentVolumeSource,Portals
API rule violation: list_type_missing,k8s.io/api/core/v1,ISCSIVolumeSource,Portals
API rule violation: list_type_missing,k8s.io/api/core/v1,LimitRangeSpec,Limits
API rule violation: list_type_missing,k8s.io/api/core/v1,LoadBalancerStatus,Ingress
API rule violation: list_type_missing,k8s.io/api/core/v1,NamespaceSpec,Finalizers
API rule violation: list_type_missing,k8s.io/api/core/v1,NamespaceStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeAffinity,PreferredDuringSchedulingIgnoredDuringExecution
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeSelector,NodeSelectorTerms
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeSelectorRequirement,Values
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeSelectorTerm,MatchExpressions
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeSelectorTerm,MatchFields
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeSpec,PodCIDRs
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeSpec,Taints
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeStatus,Addresses
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeStatus,Images
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeStatus,VolumesAttached
API rule violation: list_type_missing,k8s.io/api/core/v1,NodeStatus,VolumesInUse
API rule violation: list_type_missing,k8s.io/api/core/v1,PersistentVolumeClaimSpec,AccessModes
API rule violation: list_type_missing,k8s.io/api/core/v1,PersistentVolumeClaimStatus,AccessModes
API rule violation: list_type_missing,k8s.io/api/core/v1,PersistentVolumeClaimStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/core/v1,PersistentVolumeSpec,AccessModes
API rule violation: list_type_missing,k8s.io/api/core/v1,PersistentVolumeSpec,MountOptions
API rule violation: list_type_missing,k8s.io/api/core/v1,PodAffinity,PreferredDuringSchedulingIgnoredDuringExecution
API rule violation: list_type_missing,k8s.io/api/core/v1,PodAffinity,RequiredDuringSchedulingIgnoredDuringExecution
API rule violation: list_type_missing,k8s.io/api/core/v1,PodAffinityTerm,Namespaces
API rule violation: list_type_missing,k8s.io/api/core/v1,PodAntiAffinity,PreferredDuringSchedulingIgnoredDuringExecution
API rule violation: list_type_missing,k8s.io/api/core/v1,PodAntiAffinity,RequiredDuringSchedulingIgnoredDuringExecution
API rule violation: list_type_missing,k8s.io/api/core/v1,PodDNSConfig,Nameservers
API rule violation: list_type_missing,k8s.io/api/core/v1,PodDNSConfig,Options
API rule violation: list_type_missing,k8s.io/api/core/v1,PodDNSConfig,Searches
API rule violation: list_type_missing,k8s.io/api/core/v1,PodExecOptions,Command
API rule violation: list_type_missing,k8s.io/api/core/v1,PodPortForwardOptions,Ports
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSecurityContext,SupplementalGroups
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSecurityContext,Sysctls
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,Containers
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,EphemeralContainers
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,HostAliases
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,ImagePullSecrets
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,InitContainers
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,ReadinessGates
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,Tolerations
API rule violation: list_type_missing,k8s.io/api/core/v1,PodSpec,Volumes
API rule violation: list_type_missing,k8s.io/api/core/v1,PodStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/core/v1,PodStatus,ContainerStatuses
API rule violation: list_type_missing,k8s.io/api/core/v1,PodStatus,EphemeralContainerStatuses
API rule violation: list_type_missing,k8s.io/api/core/v1,PodStatus,InitContainerStatuses
API rule violation: list_type_missing,k8s.io/api/core/v1,PodStatus,PodIPs
API rule violation: list_type_missing,k8s.io/api/core/v1,ProjectedVolumeSource,Sources
API rule violation: list_type_missing,k8s.io/api/core/v1,RBDPersistentVolumeSource,CephMonitors
API rule violation: list_type_missing,k8s.io/api/core/v1,RBDVolumeSource,CephMonitors
API rule violation: list_type_missing,k8s.io/api/core/v1,RangeAllocation,Data
API rule violation: list_type_missing,k8s.io/api/core/v1,ReplicationControllerStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/core/v1,ResourceQuotaSpec,Scopes
API rule violation: list_type_missing,k8s.io/api/core/v1,ScopeSelector,MatchExpressions
API rule violation: list_type_missing,k8s.io/api/core/v1,ScopedResourceSelectorRequirement,Values
API rule violation: list_type_missing,k8s.io/api/core/v1,SecretProjection,Items
API rule violation: list_type_missing,k8s.io/api/core/v1,SecretVolumeSource,Items
API rule violation: list_type_missing,k8s.io/api/core/v1,ServiceAccount,ImagePullSecrets
API rule violation: list_type_missing,k8s.io/api/core/v1,ServiceAccount,Secrets
API rule violation: list_type_missing,k8s.io/api/core/v1,ServiceSpec,ExternalIPs
API rule violation: list_type_missing,k8s.io/api/core/v1,ServiceSpec,LoadBalancerSourceRanges
API rule violation: list_type_missing,k8s.io/api/core/v1,ServiceSpec,TopologyKeys
API rule violation: list_type_missing,k8s.io/api/core/v1,TopologySelectorLabelRequirement,Values
API rule violation: list_type_missing,k8s.io/api/core/v1,TopologySelectorTerm,MatchLabelExpressions
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,DaemonSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,DeploymentStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,FSGroupStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,HTTPIngressRuleValue,Paths
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,IPBlock,Except
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,IngressSpec,Rules
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,IngressSpec,TLS
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,IngressTLS,Hosts
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicyEgressRule,Ports
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicyEgressRule,To
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicyIngressRule,From
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicyIngressRule,Ports
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicySpec,Egress
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicySpec,Ingress
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,NetworkPolicySpec,PolicyTypes
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,AllowedCSIDrivers
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,AllowedCapabilities
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,AllowedFlexVolumes
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,AllowedHostPaths
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,AllowedProcMountTypes
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,AllowedUnsafeSysctls
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,DefaultAddCapabilities
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,ForbiddenSysctls
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,HostPorts
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,RequiredDropCapabilities
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,PodSecurityPolicySpec,Volumes
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,ReplicaSetStatus,Conditions
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,RunAsGroupStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,RunAsUserStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,RuntimeClassStrategyOptions,AllowedRuntimeClassNames
API rule violation: list_type_missing,k8s.io/api/extensions/v1beta1,SupplementalGroupsStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/imagepolicy/v1alpha1,ImageReviewSpec,Containers
API rule violation: list_type_missing,k8s.io/api/networking/v1,IPBlock,Except
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicyEgressRule,Ports
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicyEgressRule,To
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicyIngressRule,From
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicyIngressRule,Ports
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicySpec,Egress
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicySpec,Ingress
API rule violation: list_type_missing,k8s.io/api/networking/v1,NetworkPolicySpec,PolicyTypes
API rule violation: list_type_missing,k8s.io/api/networking/v1beta1,HTTPIngressRuleValue,Paths
API rule violation: list_type_missing,k8s.io/api/networking/v1beta1,IngressSpec,Rules
API rule violation: list_type_missing,k8s.io/api/networking/v1beta1,IngressSpec,TLS
API rule violation: list_type_missing,k8s.io/api/networking/v1beta1,IngressTLS,Hosts
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,FSGroupStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,AllowedCSIDrivers
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,AllowedCapabilities
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,AllowedFlexVolumes
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,AllowedHostPaths
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,AllowedProcMountTypes
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,AllowedUnsafeSysctls
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,DefaultAddCapabilities
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,ForbiddenSysctls
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,HostPorts
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,RequiredDropCapabilities
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,PodSecurityPolicySpec,Volumes
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,RunAsGroupStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,RunAsUserStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,RuntimeClassStrategyOptions,AllowedRuntimeClassNames
API rule violation: list_type_missing,k8s.io/api/policy/v1beta1,SupplementalGroupsStrategyOptions,Ranges
API rule violation: list_type_missing,k8s.io/api/rbac/v1,AggregationRule,ClusterRoleSelectors
API rule violation: list_type_missing,k8s.io/api/rbac/v1,ClusterRole,Rules
API rule violation: list_type_missing,k8s.io/api/rbac/v1,ClusterRoleBinding,Subjects
API rule violation: list_type_missing,k8s.io/api/rbac/v1,PolicyRule,APIGroups
API rule violation: list_type_missing,k8s.io/api/rbac/v1,PolicyRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/api/rbac/v1,PolicyRule,ResourceNames
API rule violation: list_type_missing,k8s.io/api/rbac/v1,PolicyRule,Resources
API rule violation: list_type_missing,k8s.io/api/rbac/v1,PolicyRule,Verbs
API rule violation: list_type_missing,k8s.io/api/rbac/v1,Role,Rules
API rule violation: list_type_missing,k8s.io/api/rbac/v1,RoleBinding,Subjects
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,AggregationRule,ClusterRoleSelectors
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,ClusterRole,Rules
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,ClusterRoleBinding,Subjects
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,PolicyRule,APIGroups
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,PolicyRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,PolicyRule,ResourceNames
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,PolicyRule,Resources
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,PolicyRule,Verbs
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,Role,Rules
API rule violation: list_type_missing,k8s.io/api/rbac/v1alpha1,RoleBinding,Subjects
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,AggregationRule,ClusterRoleSelectors
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,ClusterRole,Rules
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,ClusterRoleBinding,Subjects
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,PolicyRule,APIGroups
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,PolicyRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,PolicyRule,ResourceNames
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,PolicyRule,Resources
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,PolicyRule,Verbs
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,Role,Rules
API rule violation: list_type_missing,k8s.io/api/rbac/v1beta1,RoleBinding,Subjects
API rule violation: list_type_missing,k8s.io/api/storage/v1,CSINodeDriver,TopologyKeys
API rule violation: list_type_missing,k8s.io/api/storage/v1,CSINodeSpec,Drivers
API rule violation: list_type_missing,k8s.io/api/storage/v1,StorageClass,AllowedTopologies
API rule violation: list_type_missing,k8s.io/api/storage/v1,StorageClass,MountOptions
API rule violation: list_type_missing,k8s.io/api/storage/v1alpha1,CSIStorageCapacityList,Items
API rule violation: list_type_missing,k8s.io/api/storage/v1beta1,CSIDriverSpec,VolumeLifecycleModes
API rule violation: list_type_missing,k8s.io/api/storage/v1beta1,CSINodeDriver,TopologyKeys
API rule violation: list_type_missing,k8s.io/api/storage/v1beta1,CSINodeSpec,Drivers
API rule violation: list_type_missing,k8s.io/api/storage/v1beta1,StorageClass,AllowedTopologies
API rule violation: list_type_missing,k8s.io/api/storage/v1beta1,StorageClass,MountOptions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,ConversionRequest,Objects
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,ConversionResponse,ConvertedObjects
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,CustomResourceDefinitionNames,Categories
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,CustomResourceDefinitionNames,ShortNames
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,CustomResourceDefinitionSpec,Versions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,CustomResourceDefinitionStatus,Conditions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,CustomResourceDefinitionStatus,StoredVersions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,CustomResourceDefinitionVersion,AdditionalPrinterColumns
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSON,Raw
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,AllOf
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,AnyOf
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,Enum
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,OneOf
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,Required
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XListMapKeys
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrArray,JSONSchemas
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrStringArray,Property
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,WebhookClientConfig,CABundle
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,WebhookConversion,ConversionReviewVersions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,ConversionRequest,Objects
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,ConversionResponse,ConvertedObjects
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceConversion,ConversionReviewVersions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionNames,Categories
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionNames,ShortNames
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionSpec,AdditionalPrinterColumns
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionSpec,Versions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionStatus,Conditions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionStatus,StoredVersions
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceDefinitionVersion,AdditionalPrinterColumns
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSON,Raw
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,AllOf
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,AnyOf
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,Enum
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,OneOf
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,Required
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XListMapKeys
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrArray,JSONSchemas
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrStringArray,Property
API rule violation: list_type_missing,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,WebhookClientConfig,CABundle
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIGroup,ServerAddressByClientCIDRs
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIGroup,Versions
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIGroupList,Groups
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIResource,Categories
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIResource,ShortNames
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIResourceList,APIResources
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIVersions,ServerAddressByClientCIDRs
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,APIVersions,Versions
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,CreateOptions,DryRun
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,DeleteOptions,DryRun
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,FieldsV1,Raw
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,LabelSelector,MatchExpressions
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,LabelSelectorRequirement,Values
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,ObjectMeta,Finalizers
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,ObjectMeta,ManagedFields
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,ObjectMeta,OwnerReferences
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,PatchOptions,DryRun
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,RootPaths,Paths
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,StatusDetails,Causes
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,Table,ColumnDefinitions
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,Table,Rows
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,TableRow,Cells
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,TableRow,Conditions
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/apis/meta/v1,UpdateOptions,DryRun
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/runtime,RawExtension,Raw
API rule violation: list_type_missing,k8s.io/apimachinery/pkg/runtime,Unknown,Raw
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,Event,SourceIPs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,GroupResources,ResourceNames
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,GroupResources,Resources
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,Policy,OmitStages
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,Policy,Rules
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,Namespaces
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,OmitStages
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,Resources
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,UserGroups
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,Users
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1,PolicyRule,Verbs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,Event,SourceIPs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,GroupResources,ResourceNames
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,GroupResources,Resources
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,Policy,OmitStages
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,Policy,Rules
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,Namespaces
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,OmitStages
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,Resources
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,UserGroups
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,Users
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1alpha1,PolicyRule,Verbs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,Event,SourceIPs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,GroupResources,ResourceNames
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,GroupResources,Resources
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,Policy,OmitStages
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,Policy,Rules
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,Namespaces
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,NonResourceURLs
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,OmitStages
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,Resources
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,UserGroups
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,Users
API rule violation: list_type_missing,k8s.io/apiserver/pkg/apis/audit/v1beta1,PolicyRule,Verbs
API rule violation: list_type_missing,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,Controllers
API rule violation: list_type_missing,k8s.io/controller-manager/config/v1alpha1,LeaderMigrationConfiguration,ControllerLeaders
API rule violation: list_type_missing,k8s.io/kube-controller-manager/config/v1alpha1,GarbageCollectorControllerConfiguration,GCIgnoredResources
API rule violation: list_type_missing,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeBinderControllerConfiguration,VolumeHostCIDRDenylist
API rule violation: list_type_missing,k8s.io/kube-proxy/config/v1alpha1,KubeProxyConfiguration,NodePortAddresses
API rule violation: list_type_missing,k8s.io/kube-proxy/config/v1alpha1,KubeProxyIPVSConfiguration,ExcludeCIDRs
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,ExtenderTLSConfig,CAData
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,ExtenderTLSConfig,CertData
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,ExtenderTLSConfig,KeyData
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,LabelsPresence,Labels
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,LegacyExtender,ManagedResources
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,Policy,Extenders
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,Policy,Predicates
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,Policy,Priorities
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,RequestedToCapacityRatioArguments,Resources
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,RequestedToCapacityRatioArguments,Shape
API rule violation: list_type_missing,k8s.io/kube-scheduler/config/v1,ServiceAffinity,Labels
API rule violation: list_type_missing,k8s.io/kubelet/config/v1alpha1,CredentialProvider,Args
API rule violation: list_type_missing,k8s.io/kubelet/config/v1alpha1,CredentialProvider,Env
API rule violation: list_type_missing,k8s.io/kubelet/config/v1alpha1,CredentialProvider,MatchImages
API rule violation: list_type_missing,k8s.io/kubelet/config/v1alpha1,CredentialProviderConfig,Providers
API rule violation: list_type_missing,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,AllowedUnsafeSysctls
API rule violation: list_type_missing,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,ClusterDNS
API rule violation: list_type_missing,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,EnforceNodeAllocatable
API rule violation: list_type_missing,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,TLSCipherSuites
API rule violation: list_type_missing,k8s.io/metrics/pkg/apis/metrics/v1alpha1,PodMetrics,Containers
API rule violation: list_type_missing,k8s.io/metrics/pkg/apis/metrics/v1beta1,PodMetrics,Containers
API rule violation: names_match,k8s.io/api/authorization/v1beta1,SubjectAccessReviewSpec,Groups
API rule violation: names_match,k8s.io/api/core/v1,AzureDiskVolumeSource,DataDiskURI
API rule violation: names_match,k8s.io/api/core/v1,ContainerStatus,LastTerminationState
API rule violation: names_match,k8s.io/api/core/v1,DaemonEndpoint,Port
API rule violation: names_match,k8s.io/api/core/v1,Event,ReportingController
API rule violation: names_match,k8s.io/api/core/v1,FCVolumeSource,WWIDs
API rule violation: names_match,k8s.io/api/core/v1,GlusterfsPersistentVolumeSource,EndpointsName
API rule violation: names_match,k8s.io/api/core/v1,GlusterfsVolumeSource,EndpointsName
API rule violation: names_match,k8s.io/api/core/v1,ISCSIPersistentVolumeSource,DiscoveryCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,ISCSIPersistentVolumeSource,SessionCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,ISCSIVolumeSource,DiscoveryCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,ISCSIVolumeSource,SessionCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,NodeResources,Capacity
API rule violation: names_match,k8s.io/api/core/v1,NodeSpec,DoNotUseExternalID
API rule violation: names_match,k8s.io/api/core/v1,PersistentVolumeSource,CephFS
API rule violation: names_match,k8s.io/api/core/v1,PersistentVolumeSource,StorageOS
API rule violation: names_match,k8s.io/api/core/v1,PodSpec,DeprecatedServiceAccount
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,CephMonitors
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,RBDImage
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,RBDPool
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,RadosUser
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,CephMonitors
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,RBDImage
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,RBDPool
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,RadosUser
API rule violation: names_match,k8s.io/api/core/v1,VolumeSource,CephFS
API rule violation: names_match,k8s.io/api/core/v1,VolumeSource,StorageOS
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSON,Raw
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,Ref
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XEmbeddedResource
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XIntOrString
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XListMapKeys
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XListType
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XMapType
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaProps,XPreserveUnknownFields
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrArray,JSONSchemas
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrArray,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrBool,Allows
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrBool,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrStringArray,Property
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1,JSONSchemaPropsOrStringArray,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,CustomResourceColumnDefinition,JSONPath
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSON,Raw
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,Ref
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XEmbeddedResource
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XIntOrString
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XListMapKeys
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XListType
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XMapType
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaProps,XPreserveUnknownFields
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrArray,JSONSchemas
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrArray,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrBool,Allows
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrBool,Schema
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrStringArray,Property
API rule violation: names_match,k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1,JSONSchemaPropsOrStringArray,Schema
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,Format
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,d
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,i
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,s
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,int64Amount,scale
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,int64Amount,value
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,APIResourceList,APIResources
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,Duration,Duration
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,InternalEvent,Object
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,InternalEvent,Type
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,MicroTime,Time
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,StatusCause,Type
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,Time,Time
API rule violation: names_match,k8s.io/apimachinery/pkg/runtime,Unknown,ContentEncoding
API rule violation: names_match,k8s.io/apimachinery/pkg/runtime,Unknown,ContentType
API rule violation: names_match,k8s.io/apimachinery/pkg/runtime,Unknown,Raw
API rule violation: names_match,k8s.io/apimachinery/pkg/util/intstr,IntOrString,IntVal
API rule violation: names_match,k8s.io/apimachinery/pkg/util/intstr,IntOrString,StrVal
API rule violation: names_match,k8s.io/apimachinery/pkg/util/intstr,IntOrString,Type
API rule violation: names_match,k8s.io/client-go/pkg/apis/clientauthentication/v1beta1,Cluster,CertificateAuthorityData
API rule violation: names_match,k8s.io/client-go/pkg/apis/clientauthentication/v1beta1,Cluster,InsecureSkipTLSVerify
API rule violation: names_match,k8s.io/client-go/pkg/apis/clientauthentication/v1beta1,Cluster,ProxyURL
API rule violation: names_match,k8s.io/client-go/pkg/apis/clientauthentication/v1beta1,Cluster,TLSServerName
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,CloudControllerManagerConfiguration,Generic
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,CloudControllerManagerConfiguration,KubeCloudShared
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,CloudControllerManagerConfiguration,NodeStatusUpdateFrequency
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,CloudControllerManagerConfiguration,ServiceController
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,CloudProviderConfiguration,CloudConfigFile
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,CloudProviderConfiguration,Name
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,AllocateNodeCIDRs
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,AllowUntaggedCloud
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,CIDRAllocatorType
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,CloudProvider
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,ClusterCIDR
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,ClusterName
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,ConfigureCloudRoutes
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,ExternalCloudVolumePlugin
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,NodeMonitorPeriod
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,NodeSyncPeriod
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,RouteReconciliationPeriod
API rule violation: names_match,k8s.io/cloud-provider/config/v1alpha1,KubeCloudSharedConfiguration,UseServiceAccountCredentials
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,Address
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,ClientConnection
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,ControllerStartInterval
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,Controllers
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,Debugging
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,LeaderElection
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,MinResyncPeriod
API rule violation: names_match,k8s.io/controller-manager/config/v1alpha1,GenericControllerManagerConfiguration,Port
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,AttachDetachControllerConfiguration,DisableAttachDetachReconcilerSync
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,AttachDetachControllerConfiguration,ReconcilerSyncLoopPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningConfiguration,CertFile
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningConfiguration,KeyFile
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,ClusterSigningCertFile
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,ClusterSigningDuration
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,ClusterSigningKeyFile
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,KubeAPIServerClientSignerConfiguration
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,KubeletClientSignerConfiguration
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,KubeletServingSignerConfiguration
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CSRSigningControllerConfiguration,LegacyUnknownSignerConfiguration
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,CronJobControllerConfiguration,ConcurrentCronJobSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,DaemonSetControllerConfiguration,ConcurrentDaemonSetSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,DeploymentControllerConfiguration,ConcurrentDeploymentSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,DeploymentControllerConfiguration,DeploymentControllerSyncPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,DeprecatedControllerConfiguration,DeletingPodsBurst
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,DeprecatedControllerConfiguration,DeletingPodsQPS
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,DeprecatedControllerConfiguration,RegisterRetryCount
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointControllerConfiguration,ConcurrentEndpointSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointControllerConfiguration,EndpointUpdatesBatchPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointSliceControllerConfiguration,ConcurrentServiceEndpointSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointSliceControllerConfiguration,EndpointUpdatesBatchPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointSliceControllerConfiguration,MaxEndpointsPerSlice
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointSliceMirroringControllerConfiguration,MirroringConcurrentServiceEndpointSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointSliceMirroringControllerConfiguration,MirroringEndpointUpdatesBatchPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,EndpointSliceMirroringControllerConfiguration,MirroringMaxEndpointsPerSubset
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,GarbageCollectorControllerConfiguration,ConcurrentGCSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,GarbageCollectorControllerConfiguration,EnableGarbageCollector
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,GarbageCollectorControllerConfiguration,GCIgnoredResources
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,GroupResource,Group
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,GroupResource,Resource
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerCPUInitializationPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerDownscaleForbiddenWindow
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerDownscaleStabilizationWindow
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerInitialReadinessDelay
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerSyncPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerTolerance
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerUpscaleForbiddenWindow
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,HPAControllerConfiguration,HorizontalPodAutoscalerUseRESTClients
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,JobControllerConfiguration,ConcurrentJobSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,AttachDetachController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,CSRSigningController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,CronJobController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,DaemonSetController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,DeploymentController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,DeprecatedController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,EndpointController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,EndpointSliceController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,EndpointSliceMirroringController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,GarbageCollectorController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,Generic
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,HPAController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,JobController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,KubeCloudShared
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,NamespaceController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,NodeIPAMController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,NodeLifecycleController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,PersistentVolumeBinderController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,PodGCController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,ReplicaSetController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,ReplicationController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,ResourceQuotaController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,SAController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,ServiceController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,StatefulSetController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,KubeControllerManagerConfiguration,TTLAfterFinishedController
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NamespaceControllerConfiguration,ConcurrentNamespaceSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NamespaceControllerConfiguration,NamespaceSyncPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeIPAMControllerConfiguration,NodeCIDRMaskSize
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeIPAMControllerConfiguration,NodeCIDRMaskSizeIPv4
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeIPAMControllerConfiguration,NodeCIDRMaskSizeIPv6
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeIPAMControllerConfiguration,SecondaryServiceCIDR
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeIPAMControllerConfiguration,ServiceCIDR
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,EnableTaintManager
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,LargeClusterSizeThreshold
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,NodeEvictionRate
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,NodeMonitorGracePeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,NodeStartupGracePeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,PodEvictionTimeout
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,SecondaryNodeEvictionRate
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,NodeLifecycleControllerConfiguration,UnhealthyZoneThreshold
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeBinderControllerConfiguration,PVClaimBinderSyncPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeBinderControllerConfiguration,VolumeConfiguration
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeBinderControllerConfiguration,VolumeHostAllowLocalLoopback
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeBinderControllerConfiguration,VolumeHostCIDRDenylist
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,IncrementTimeoutHostPath
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,IncrementTimeoutNFS
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,MaximumRetry
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,MinimumTimeoutHostPath
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,MinimumTimeoutNFS
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,PodTemplateFilePathHostPath
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PersistentVolumeRecyclerConfiguration,PodTemplateFilePathNFS
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,PodGCControllerConfiguration,TerminatedPodGCThreshold
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,ReplicaSetControllerConfiguration,ConcurrentRSSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,ReplicationControllerConfiguration,ConcurrentRCSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,ResourceQuotaControllerConfiguration,ConcurrentResourceQuotaSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,ResourceQuotaControllerConfiguration,ResourceQuotaSyncPeriod
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,SAControllerConfiguration,ConcurrentSATokenSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,SAControllerConfiguration,RootCAFile
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,SAControllerConfiguration,ServiceAccountKeyFile
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,StatefulSetControllerConfiguration,ConcurrentStatefulSetSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,TTLAfterFinishedControllerConfiguration,ConcurrentTTLSyncs
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,VolumeConfiguration,EnableDynamicProvisioning
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,VolumeConfiguration,EnableHostPathProvisioning
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,VolumeConfiguration,FlexVolumePluginDir
API rule violation: names_match,k8s.io/kube-controller-manager/config/v1alpha1,VolumeConfiguration,PersistentVolumeRecyclerConfiguration
API rule violation: names_match,k8s.io/kube-proxy/config/v1alpha1,KubeProxyConfiguration,IPTables
API rule violation: names_match,k8s.io/kube-scheduler/config/v1,LegacyExtender,EnableHTTPS
API rule violation: names_match,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,IPTablesDropBit
API rule violation: names_match,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,IPTablesMasqueradeBit
API rule violation: names_match,k8s.io/kubelet/config/v1beta1,KubeletConfiguration,ResolverConfig
API rule violation: names_match,k8s.io/metrics/pkg/apis/custom_metrics/v1beta1,MetricValue,WindowSeconds
API rule violation: names_match,k8s.io/metrics/pkg/apis/external_metrics/v1beta1,ExternalMetricValue,WindowSeconds
