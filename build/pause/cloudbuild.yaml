# See https://cloud.google.com/cloud-build/docs/build-config
timeout: 1200s
options:
  substitution_option: ALLOW_LOOSE
  machineType: 'N1_HIGHCPU_8'
steps:
  - name: 'gcr.io/k8s-testimages/gcb-docker-gcloud:v20200422-b25d964'
    entrypoint: 'bash'
    dir: ./build/pause
    env:
      - DOCKER_CLI_EXPERIMENTAL=enabled
      - REGISTRY=gcr.io/$PROJECT_ID
      - IMAGE=gcr.io/$PROJECT_ID/pause
      - HOME=/root
    args:
      - '-c'
      - |
        gcloud auth configure-docker \
        && docker buildx create --name img-builder --use \
        && make all-push
