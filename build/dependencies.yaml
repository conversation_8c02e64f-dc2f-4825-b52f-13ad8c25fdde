dependencies:
  # agnhost: bump this one first
  - name: "agnhost"
    version: "2.25"
    refPaths:
    - path: test/images/agnhost/VERSION
      match: \d.\d
    - path: test/images/agnhost/agnhost.go
      match: "Version:"

  # then after merge and successful postsubmit image push / promotion, bump this
  - name: "agnhost: dependents"
    version: "2.21"
    refPaths:
    - path: test/utils/image/manifest.go
      match: configs\[Agnhost\] = Config{promoterE2eRegistry, "agnhost", "\d+\.\d+"}

  # Bazel
  - name: "repo-infra"
    version: 0.1.8
    refPaths:
    - path: build/root/WORKSPACE
      match: strip_prefix = "repo-infra-\d+.\d+.\d+"
    - path: build/root/WORKSPACE
      match: https://github.com/kubernetes/repo-infra/archive/v\d+.\d+.\d+.tar.gz

  # CNI plugins
  - name: "cni"
    version: 0.8.7
    refPaths:
    - path: build/workspace.bzl
      match: CNI_VERSION =
    - path: cluster/gce/gci/configure.sh
      match: DEFAULT_CNI_VERSION=
    - path: cluster/gce/config-common.sh
      match: WINDOWS_CNI_VERSION=
    - path: test/e2e_node/remote/utils.go
      match: cniVersion[\t\n\f\r ]*=

  # CoreDNS
  - name: "coredns-kube-up"
    version: 1.7.0
    refPaths:
    - path: cluster/addons/dns/coredns/coredns.yaml.base
      match: k8s.gcr.io/coredns
    - path: cluster/addons/dns/coredns/coredns.yaml.in
      match: k8s.gcr.io/coredns
    - path: cluster/addons/dns/coredns/coredns.yaml.sed
      match: k8s.gcr.io/coredns

  - name: "coredns-kubeadm"
    version: 1.7.0
    refPaths:
    - path: cmd/kubeadm/app/constants/constants.go
      match: CoreDNSVersion =

  # CRI Tools
  - name: "crictl"
    version: 1.19.0
    refPaths:
    - path: build/workspace.bzl
      match: CRI_TOOLS_VERSION =
    - path: cluster/gce/gci/configure.sh
      match: DEFAULT_CRICTL_VERSION=
    - path: cluster/gce/windows/k8s-node-setup.psm1
      match: CRICTL_VERSION =

  # Docker
  - name: "docker"
    version: 19.03
    refPaths:
    - path: vendor/k8s.io/system-validators/validators/docker_validator.go
      match: latestValidatedDockerVersion

  # etcd
  - name: "etcd"
    version: 3.4.13
    refPaths:
    - path: cluster/gce/manifests/etcd.manifest
      match: etcd_docker_tag|etcd_version
    - path: build/workspace.bzl
      match: ETCD_VERSION
    - path: cluster/gce/upgrade-aliases.sh
      match: ETCD_IMAGE|ETCD_VERSION
    - path: cmd/kubeadm/app/constants/constants.go
    - path: hack/lib/etcd.sh
      match: ETCD_VERSION=
    - path: staging/src/k8s.io/sample-apiserver/artifacts/example/deployment.yaml
      match: quay.io/coreos/etcd
    - path: test/e2e/framework/nodes_util.go
      match: const etcdImage

  - name: "etcd-image"
    version: 3.4.13
    refPaths:
    - path: cluster/images/etcd/Makefile
      match: BUNDLED_ETCD_VERSIONS\?|LATEST_ETCD_VERSION\?
    - path: cluster/images/etcd/migrate/options.go

  # Golang
  - name: "golang: upstream version"
    version: 1.15.13
    refPaths:
    - path: build/build-image/cross/VERSION
    - path: build/root/WORKSPACE
      match: (override_)?go_version = "\d+.\d+(alpha|beta|rc)?\.?\d+"
    - path: cluster/addons/fluentd-elasticsearch/es-image/Dockerfile
      match: 'FROM golang\:\d+.\d+(alpha|beta|rc)?\.?(\d+)?'
    - path: test/images/Makefile
      match: GOLANG_VERSION=\d+.\d+(alpha|beta|rc)?\.?\d+
    - path: staging/publishing/rules.yaml
      match: 'default-go-version\: \d+.\d+(alpha|beta|rc)?\.?(\d+)?'

  - name: "k8s.gcr.io/kube-cross: dependents"
    version: v1.15.13-legacy-1
    refPaths:
    - path: build/build-image/cross/VERSION
    - path: test/images/sample-apiserver/Makefile
      match: k8s\.gcr\.io\/build-image\/kube-cross:v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)

  # Base images
  - name: "k8s.gcr.io/debian-base: dependents"
    version: buster-v1.7.0
    refPaths:
    - path: build/workspace.bzl
      match: tag =
    - path: cluster/images/etcd/Makefile
      match: BASEIMAGE\?\=k8s\.gcr\.io\/build-image\/debian-base:[a-zA-Z]+\-v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)
    - path: cluster/images/etcd/Makefile
      match: BASEIMAGE\?\=k8s\.gcr\.io\/build-image\/debian-base-arm:[a-zA-Z]+\-v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)
    - path: cluster/images/etcd/Makefile
      match: BASEIMAGE\?\=k8s\.gcr\.io\/build-image\/debian-base-arm64:[a-zA-Z]+\-v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)
    - path: cluster/images/etcd/Makefile
      match: BASEIMAGE\?\=k8s\.gcr\.io\/build-image\/debian-base-ppc64le:[a-zA-Z]+\-v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)
    - path: cluster/images/etcd/Makefile
      match: BASEIMAGE\?\=k8s\.gcr\.io\/build-image\/debian-base-s390x:[a-zA-Z]+\-v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)

  - name: "k8s.gcr.io/debian-iptables: dependents"
    version: buster-v1.6.1
    refPaths:
    - path: build/common.sh
      match: debian_iptables_version=
    - path: build/workspace.bzl
      match: tag =
    - path: test/utils/image/manifest.go
      match: configs\[DebianIptables\] = Config{buildImageRegistry, "debian-iptables", "[a-zA-Z]+\-v((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)"}

  - name: "k8s.gcr.io/go-runner: dependents"
    version: v2.3.1-go1.15.13-buster.0
    refPaths:
    - path: build/common.sh
      match: go_runner_version=
    - path: build/workspace.bzl
      match: tag =

  - name: "k8s.gcr.io/pause"
    version: 3.4
    refPaths:
    - path: build/pause/Makefile
      match: TAG =

  - name: "k8s.gcr.io/pause: dependents"
    version: 3.2
    refPaths:
    - path: cmd/kubeadm/app/constants/constants_unix.go
      match: PauseVersion\s+=
    - path: cmd/kubeadm/app/util/template_test.go
      match: validTmpl\s+=
    - path: cmd/kubeadm/app/util/template_test.go
      match: validTmplOut\s+=
    - path: cmd/kubeadm/app/util/template_test.go
      match: doNothing\s+=
    - path: cmd/kubelet/app/options/container_runtime.go
      match: defaultPodSandboxImageVersion\s+=
    - path: hack/testdata/pod-with-precision.json
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: pkg/kubelet/dockershim/docker_sandbox.go
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: staging/src/k8s.io/kubectl/testdata/set/multi-resource-yaml.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: staging/src/k8s.io/kubectl/testdata/set/namespaced-resource.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/cmd/core.sh
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/fixtures/pkg/kubectl/cmd/set/multi-resource-yaml.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/fixtures/pkg/kubectl/cmd/set/namespaced-resource.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/benchmark-controller.json
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-default.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-node-affinity.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-pod-affinity.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-pod-anti-affinity.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-preferred-pod-affinity.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-preferred-pod-anti-affinity.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-preferred-topology-spreading.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-secret-volume.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/integration/scheduler_perf/config/pod-with-topology-spreading.yaml
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/utils/runners.go
      match: k8s.gcr.io\/pause:\d+\.\d+
    - path: test/utils/image/manifest.go
      match: configs\[Pause\] = Config{gcRegistry, "pause", "\d+\.\d+"}
