# Copyright 2017 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A project wanting to generate openapi code for vendored
# k8s.io/kubernetes will need to set the following variables in
# //build/openapi.bzl in their project and customize the go prefix:
#
# openapi_vendor_prefix = "vendor/k8s.io/kubernetes/"

openapi_vendor_prefix = ""
