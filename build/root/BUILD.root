# gazelle:build_file_name BUILD,BUILD.bazel

# gazelle:exclude _artifacts
# gazelle:exclude _gopath
# gazelle:exclude _output
# gazelle:exclude _tmp

# This tag is required to build github.com/opencontainers/selinux correctly
# gazelle:build_tags selinux

# gazelle:prefix k8s.io/kubernetes

# Disable proto rules, since the Go sources are currently generated by
# hack/update-generated-protobuf.sh and checked into the repo.
# gazelle:proto disable_global

package(default_visibility = ["//visibility:public"])

load("@io_k8s_repo_infra//defs:build.bzl", "gcs_upload")
load("@io_k8s_repo_infra//defs:pkg.bzl", "pkg_tar")
load("//build:platforms.bzl", "for_platforms")

filegroup(
    name = "_binary-artifacts-and-hashes",
    srcs = select(for_platforms(
        for_client = ["//build:client-targets-and-hashes"],
        for_node = [
            "//build:node-targets-and-hashes",
        ],
        for_server = [
            "//build:docker-artifacts-and-hashes",
            "//build:server-targets-and-hashes",
        ],
    )),
    visibility = ["//visibility:private"],
)

# TODO: collect all relevant docker_push targets into one target that can be run:
#   //build:push-server-images
#   //cluster/images/conformance:push-conformance
gcs_upload(
    name = "push-build",
    data = [
        ":_binary-artifacts-and-hashes",
        "//build/release-tars:release-tars-and-hashes",
        "//cluster/gce/gci:gcs-release-artifacts-and-hashes",
        "//cluster/gce/windows:gcs-release-artifacts-and-hashes",
    ],
    tags = ["manual"],
    # Use for_platforms to format the upload path based on the configured
    # platform (os/arch).
    # For example, this will turn into something like
    # upload_paths = select({
    #    "@io_bazel_rules_go//go/platform:windows_386": {
    #        ...,"//:binary-artifacts-and-hashes": "bin/windows/386"},
    #    "@io_bazel_rules_go//go/platform:linux_ppc64le": {
    #        ...,"//:binary-artifacts-and-hashes": "bin/linux/ppc64le"},
    #})
    # and bazel will select the correct entry.
    upload_paths = select(for_platforms(for_all = {
        "//build/release-tars:release-tars-and-hashes": "",
        "//cluster/gce/gci:gcs-release-artifacts-and-hashes": "extra/gce",
        "//cluster/gce/windows:gcs-release-artifacts-and-hashes": "extra/gce/windows",
        "//:_binary-artifacts-and-hashes": "bin/{OS}/{ARCH}",
    })),
)

filegroup(
    name = "package-srcs",
    srcs = glob(
        ["**"],
        exclude = [
            "bazel-*/**",
            "_*/**",
            ".config/**",
            ".git/**",
            ".gsutil/**",
            ".make/**",
        ],
    ),
    visibility = ["//visibility:private"],
)

filegroup(
    name = "all-srcs",
    srcs = [
        ":package-srcs",
        "//api/openapi-spec:all-srcs",
        "//build:all-srcs",
        "//cluster:all-srcs",
        "//cmd:all-srcs",
        "//docs:all-srcs",
        "//hack:all-srcs",
        "//pkg:all-srcs",
        "//plugin:all-srcs",
        "//staging:all-srcs",
        "//test:all-srcs",
        "//third_party:all-srcs",
        "//translations:all-srcs",
        "//vendor:all-srcs",
    ],
    tags = ["automanaged"],
)

genrule(
    name = "save_git_version",
    outs = ["version"],
    cmd = "grep ^STABLE_BUILD_SCM_REVISION bazel-out/stable-status.txt | awk '{print $$2}' >$@",
    stamp = 1,
)

platform(
    name = "rbe_with_network",
    parents = ["@rbe_default//config:platform"],
    # https://cloud.google.com/remote-build-execution/docs/remote-execution-environment#remote_execution_properties
    remote_execution_properties = """
      properties: {
        name: "dockerNetwork"
        value: "standard"
      }
      properties: {
        name: "dockerPrivileged"
        value: "true"
      }
      {PARENT_REMOTE_EXECUTION_PROPERTIES}
    """,
)

pkg_tar(
    name = "LICENSES",
    srcs = glob(
        ["LICENSES/**/LICENSE"],
        allow_empty = False,
        exclude_directories = 0,
    ),
    mode = "0755",
    strip_prefix = "//",
    tags = [
        "manual",
        "no-cache",
    ],
)
