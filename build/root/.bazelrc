startup --expand_configs_in_place

# https://github.com/kubernetes/kubernetes/pull/79807
# https://github.com/bazelbuild/rules_docker/issues/842
# https://github.com/bazelbuild/bazel/issues/7899
build --host_force_python=PY2

# Show us information about failures.
test --test_output=errors

# Include git version info
build --stamp
build --workspace_status_command hack/print-workspace-status.sh

# Make /tmp hermetic
build --sandbox_tmpfs_path=/tmp

# This tag is required to build github.com/opencontainers/selinux correctly
build --define gotags=selinux

# Ensure that <PERSON><PERSON> never runs as root, which can cause unit tests to fail.
# This flag requires Bazel 0.5.0+
build --sandbox_fake_username

# Output go_build_mode metadata for binaries. This metadata is used for
# executable selection e.g. in CI.
build --aspects //build:go.bzl%go_build_mode_aspect

# Enable go race detection.
build:unit --@io_bazel_rules_go//go/config:race
test:unit --@io_bazel_rules_go//go/config:race

test:unit --build_tests_only
test:unit --test_tag_filters=-e2e,-integration
test:unit --runs_per_test=2

test:integration --local_test_jobs 4
test:integration --test_tag_filters=integration

# Darwin and Windows only cross-compile pure Go
build:cross:darwin_386 --platforms=@io_bazel_rules_go//go/toolchain:darwin_386
build:cross:darwin_amd64 --platforms=@io_bazel_rules_go//go/toolchain:darwin_amd64
build:cross:windows_386 --platforms=@io_bazel_rules_go//go/toolchain:windows_386
build:cross:windows_amd64 --platforms=@io_bazel_rules_go//go/toolchain:windows_amd64

# These cross-compilation targets are most likely non-functional.
build:crosstool --compiler=gcc
build:cross:linux_386 --config=crosstool --platforms=@io_bazel_rules_go//go/toolchain:linux_386
build:cross:linux_amd64 --config=crosstool --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 --cpu=amd64
build:cross:linux_arm --config=crosstool --platforms=@io_bazel_rules_go//go/toolchain:linux_arm --cpu=arm
build:cross:linux_arm64 --config=crosstool --platforms=@io_bazel_rules_go//go/toolchain:linux_arm64 --cpu=arm64
build:cross:linux_ppc64le --config=crosstool --platforms=@io_bazel_rules_go//go/toolchain:linux_ppc64le --cpu=ppc64le
build:cross:linux_s390x --config=crosstool --platforms=@io_bazel_rules_go//go/toolchain:linux_s390x --cpu=s390x

# --config=remote-cache enables a remote bazel cache
# Note needs a --remote_instance_name=projects/PROJ/instances/default_instance flag
build:remote-cache --remote_cache=remotebuildexecution.googleapis.com
build:remote-cache --remote_timeout=3600
build:remote-cache --auth_enabled=true

# --config=remote adds remote execution to the --config=remote-cache
# Note needs a --remote_instance_name=projects/PROJ/instances/default_instance flag
build:remote --config=remote-cache
build:remote --remote_executor=remotebuildexecution.googleapis.com
build:remote --jobs=500
build:remote --host_javabase=@rbe_default//java:jdk
build:remote --javabase=@rbe_default//java:jdk
build:remote --host_java_toolchain=@bazel_tools//tools/jdk:toolchain_hostjdk8
build:remote --java_toolchain=@bazel_tools//tools/jdk:toolchain_hostjdk8
build:remote --crosstool_top=@rbe_default//cc:toolchain
build:remote --action_env=BAZEL_DO_NOT_DETECT_CPP_TOOLCHAIN=1
build:remote --extra_toolchains=@rbe_default//config:cc-toolchain
build:remote --extra_execution_platforms=:rbe_with_network
build:remote --host_platform=:rbe_with_network
build:remote --platforms=:rbe_with_network
build:remote --spawn_strategy=remote
build:remote --strategy=Javac=remote
build:remote --strategy=Closure=remote
build:remote --strategy=Genrule=remote
build:remote --define=EXECUTOR=remote

# tar locally due to https://github.com/bazelbuild/bazel/issues/8462
build:remote --strategy=PackageTar=sandboxed


# --config=ci-instance adds a remote instance name
build:ci-instance --remote_instance_name=projects/k8s-prow-builds/instances/default_instance

# The config we want to use in CI
build:ci --config=remote --config=ci-instance
