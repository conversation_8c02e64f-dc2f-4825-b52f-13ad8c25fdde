### Version 9.1.2 (Thu August 6 2020 <PERSON> <<EMAIL>>)
 - Fix `start_addon` overwriting resources with `addonmanager.kubernetes.io/mode=EnsureExists`.

### Version 9.1.1 (Wed May 19 2020 <PERSON><PERSON> <<EMAIL>>)
 - Fix kube-addons.sh and kubectl permissions

### Version 9.1.0 (Wed May 13 2020 <PERSON><PERSON> <<EMAIL>>)
 - Enable overriding the default list of whitelisted resources

### Version 9.0.2  (Thu August 1 2019 <PERSON><PERSON><PERSON> <maciej<PERSON><EMAIL>>
 - Fix a bug in leader election (https://github.com/kubernetes/kubernetes/pull/80575)

### Version 9.0.1  (Wed April 10 2019 Zihong <PERSON> <<EMAIL>>)
 - Update to use debian-base:v1.0.0.

### Version 9.0  (Wed January 16 2019 <PERSON> <<EMAIL>>)
 - Prune workload resources via apps/v1 APIs
 - Update kubectl to v1.13.2.

### Version 8.9  (Fri October 19 2018 <PERSON> <<EMAIL>>)
 - Update to use debian-base:0.4.0.
 - Update kubectl to v1.11.3.

### <AUTHOR> <EMAIL>)
 - Update to use debian-base:0.3.2.

### <AUTHOR> <EMAIL>)
 - Support extra `--prune-whitelist` resources in kube-addon-manager.
 - Update kubectl to v1.10.7.

### <AUTHOR> <EMAIL>)
 - Allow reconcile/ensure loop to work with resource under non-kube-system namespace.
 - Update kubectl to v1.9.3.

### Version 8.4  (Thu November 30 2017 zou nengren @zouyee)
 - Update kubectl to v1.8.4.

### <AUTHOR> <EMAIL>)
 - Support for HA masters.

### <AUTHOR> <EMAIL>)
 - Update kubectl to v1.6.4.
 - Refresh base images.

### <AUTHOR> <EMAIL>)
 - Create EnsureExists class addons before Reconcile class addons.

### <AUTHOR> <EMAIL>)
 - Support 'ensure exist' class addon and use addon-manager specific label.

### <AUTHOR> <EMAIL>)
 - Update kubectl to v1.6.0-alpha.2 to use HPA in autoscaling/v1 instead of extensions/v1beta1.

### <AUTHOR> <EMAIL>)
 - Update kubectl to v1.6.0-alpha.1 for supporting optional ConfigMap.

### <AUTHOR> <EMAIL>)
 - Updated the arm base image to `armhf/busybox` and now using qemu v2.7 for emulation.

### <AUTHOR> <EMAIL>)
 - Update kubectl to the stable version.

### <AUTHOR> <EMAIL>)
 - Support pruning old Deployments.

### <AUTHOR> <EMAIL>)
 - Upgrade Addon Manager to use `kubectl apply`.

### <AUTHOR> <EMAIL>)
 - Added support for ConfigMap and upgraded kubectl version to v1.4.4 (pr #35255)

### <AUTHOR> <EMAIL>)
 - Fixed the way addon-manager handles non-namespaced objects

### Version 5 (Fri Jun 24 2016 Jerzy Szczepkowski @jszczepkowski)
 - Added PetSet support to addon manager

### Version 4 (Tue Jun 21 2016 Mike Danese @mikedanese)
 - Increased addon check interval

### Version 3 (Sun Jun 19 2016 Lucas Käldström @luxas)
 - Bumped up addon-manager to v3

### Version 2 (Fri May 20 2016 Lucas Käldström @luxas)
 - Removed deprecated kubectl command, added support for DaemonSets

### Version 1 (Thu May 5 2016 Mike Danese @mikedanese)
 - Run kube-addon-manager in a pod


[![Analytics](https://kubernetes-site.appspot.com/***********-10/GitHub/cluster/addons/addon-manager/CHANGELOG.md?pixel)]()
