apiVersion: apps/v1
kind: Deployment
metadata:
  name: calico-typha
  namespace: kube-system
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    k8s-app: calico-typha
spec:
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      k8s-app: calico-typha
  template:
    metadata:
      labels:
        k8s-app: calico-typha
    spec:
      priorityClassName: system-cluster-critical
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      hostNetwork: true
      serviceAccountName: calico
      containers:
      - image: gcr.io/projectcalico-org/typha:v3.16.2
        name: calico-typha
        ports:
        - containerPort: 5473
          name: calico-typha
          protocol: TCP
        env:
          - name: TYPHA_LOGFILEPATH
            value: "none"
          - name: TYPHA_LOGSEVERITYSYS
            value: "none"
          - name: TYPHA_LOGSEVERITYSCREEN
            value: "info"
          - name: TYPHA_PROMETHEUSMETRICSENABLED
            value: "true"
          - name: TYPHA_CONNECTIONREBALANCINGMODE
            value: "kubernetes"
          - name: TYPHA_PROMETHEUSMETRICSPORT
            value: "9093"
          - name: TYPHA_DATASTORETYPE
            value: "kubernetes"
          - name: TYPHA_REPORTINGINTERVALSECS
            value: "0"
          - name: TYPHA_MAXCONNECTIONSLOWERLIMIT
            value: "1"
          - name: TYPHA_HEALTHENABLED
            value: "true"
          - name: USE_POD_CIDR
            value: "true"
        volumeMounts:
        - mountPath: /etc/calico
          name: etc-calico
          readOnly: true
        livenessProbe:
          httpGet:
            path: /liveness
            port: 9098
            host: localhost
          periodSeconds: 30
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: /readiness
            port: 9098
            host: localhost
          periodSeconds: 10
      volumes:
      - name: etc-calico
        hostPath:
          path: /etc/calico
